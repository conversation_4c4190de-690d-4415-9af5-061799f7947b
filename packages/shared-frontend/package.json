{"name": "@pulsetrack/shared-frontend", "version": "0.1.0", "private": true, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "scripts": {"build": "tsup src/index.ts --format esm,cjs --dts --external react,react-dom,react-dom,react-dom,react-dom,react-dom,react-dom", "dev": "tsup src/index.ts --format esm,cjs --dts --external react,react-dom,react-dom,react-dom,react-dom,react-dom,react-dom --watch", "lint": "eslint . --max-warnings 0", "clean": "rm -rf dist"}, "dependencies": {"@clerk/clerk-react": "^5.25.6", "@clerk/types": "^4.0.0", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-slot": "^1.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.3.1", "date-fns-tz": "^3.1.3", "lucide-react": "^0.487.0", "tailwind-merge": "^3.2.0"}, "devDependencies": {"@types/react": "^19.0.0", "eslint": "^8.57.0", "prettier": "^3.5.3", "react": "^19.1.0", "react-dom": "19.0.0", "tsup": "^8.0.1", "typescript": "^5.3.3", "vite": "6.2.0"}, "peerDependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}}